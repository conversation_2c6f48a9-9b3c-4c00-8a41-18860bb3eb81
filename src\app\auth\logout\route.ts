import { NextRequest, NextResponse } from "next/server";
import { remove<PERSON><PERSON>ie } from "@/shared/lib/cookies/crud/remove";

export async function POST(request: NextRequest): Promise<NextResponse> {
	try {
		// Remove access token
		await remove<PERSON>ookie({ name: "access_token" });
		
		// Remove refresh token if it exists
		await remove<PERSON><PERSON>ie({ name: "refresh_token" });
		
		return NextResponse.json(
			{ 
				success: true, 
				message: "Logout realizado com sucesso" 
			}, 
			{ status: 200 }
		);
	} catch (error) {
		console.error("Erro no logout:", error);
		return NextResponse.json(
			{ 
				success: false, 
				message: "Erro ao realizar logout" 
			}, 
			{ status: 500 }
		);
	}
}

export async function DELETE(request: NextRequest): Promise<NextResponse> {
	// Redireciona DELETE para POST para manter compatibilidade
	return POST(request);
}
