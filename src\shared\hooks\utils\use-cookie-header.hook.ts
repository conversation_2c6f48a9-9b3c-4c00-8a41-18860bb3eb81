import { useCallback, useMemo } from "react";
import { CookieHeaderService } from "@/shared/lib/cookies/services/cookie-header.service";
import { ICookieData } from "@/shared/types/requests/cookie-header.type";

export interface IUseCookieHeaderHook {
	/**
	 * Processa os dados de cookies e retorna o header formatado
	 */
	processCookieHeader: (cookieData: ICookieData) => string | null;

	/**
	 * Verifica se um cookie específico deve ser incluído
	 */
	shouldIncludeCookie: (cookieName: string, cookieValue: string) => boolean;

	/**
	 * Constrói o header Cookie a partir de um objeto de cookies
	 */
	buildCookieHeader: (cookies: Record<string, string>) => string;
}

/**
 * Hook para gerenciar headers de cookies
 * Segue o princípio de responsabilidade única (SRP) do SOLID
 */
export const useCookieHeader = (): IUseCookieHeaderHook => {
	const cookieService = useMemo(() => new CookieHeaderService(), []);

	const processCookieHeader = useCallback(
		(cookieData: ICookieData): string | null => {
			return cookieService.processCookieData(cookieData);
		},
		[cookieService]
	);

	const shouldIncludeCookie = useCallback(
		(cookieName: string, cookieValue: string): boolean => {
			return cookieService.shouldIncludeCookie(cookieName, cookieValue);
		},
		[cookieService]
	);

	const buildCookieHeader = useCallback(
		(cookies: Record<string, string>): string => {
			return cookieService.buildCookieHeader(cookies);
		},
		[cookieService]
	);

	return {
		processCookieHeader,
		shouldIncludeCookie,
		buildCookieHeader,
	};
};
