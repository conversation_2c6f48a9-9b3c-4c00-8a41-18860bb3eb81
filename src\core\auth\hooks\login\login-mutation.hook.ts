// import { useNavigatePaths } from "@/shared/hooks/utils";
// import { useMutation } from "@tanstack/react-query";
// import { ILoginDto } from "../../dto/login.dto";

// import { toast } from "@/core/toast";
// import { useUserSync } from "../user/use-user-sync.hook";

// export const useLoginMutation = () => {
// 	const { replaceToCurrent } = useNavigatePaths();
// 	const { refreshUser } = useUserSync();
// 	const mutation = useMutation({
// 		mutationKey: ["Login"],
// 		mutationFn: async ({ username, password }: ILoginDto) => {
// 			const res = await loginEntry(username, password);
// 			if (!res.success) throw new Error(res.data.message);
// 			return res.data;
// 		},
// 		onSuccess: async () => {
// 			await refreshUser();
// 			replaceToCurrent();
// 		},
// 		onError: async error => toast.error(error.message),
// 	});

// 	const login = (loginDto: ILoginDto) => toast.promise(mutation.mutateAsync(loginDto), { loading: "Fazendo login..." }, { title: "Aguarde" });

// 	return { login, isLoading: mutation.isPending };
// };
