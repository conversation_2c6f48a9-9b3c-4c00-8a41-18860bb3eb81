"use client";

import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useSetAtom } from "jotai";
import { useEffect, useState } from "react";
import { isAuthenticatedAtom } from "../../atoms/auth.atom";
import { userAtom } from "../../atoms/user.atom";
import { getCurrentUser } from "../../lib/user-actions";
import { getAuthToken } from "../../lib/auth-actions";
import { ApiResponse } from "@/shared/types/requests/request.type";
import { IUser } from "../../types/user.types";

export function useUserSync() {
	const setUser = useSetAtom(userAtom);
	const setIsAuthenticated = useSetAtom(isAuthenticatedAtom);
	const queryClient = useQueryClient();
	const [hasToken, setHasToken] = useState<boolean | null>(null);

	const clearUserData = () => {
		setIsAuthenticated(false);
		setUser(null);
	};

	const updateAuthState = (userData: ApiResponse<IUser>) => {
		const isValid = !!(userData?.success && userData?.data);
		setIsAuthenticated(isValid);
		setUser(isValid ? userData.data : null);
	};

	useEffect(() => {
		const checkToken = async () => {
			const token = await getAuthToken();
			setHasToken(!!token);
			if (!token) clearUserData();
		};

		checkToken();

		// Verificar tokens periodicamente para detectar logout
		const interval = setInterval(checkToken, 5000); // Verifica a cada 5 segundos

		return () => clearInterval(interval);
	}, []);

	const { data, isLoading, error } = useQuery({
		queryKey: ["user"],
		queryFn: getCurrentUser,
		enabled: hasToken === true,
		retry: false,
		staleTime: 5 * 60 * 1000,
		gcTime: 10 * 60 * 1000,
	});

	useEffect(() => {
		if (hasToken === false) {
			clearUserData();
		} else if (data) {
			updateAuthState(data);
		}
	}, [data, hasToken]);

	const refreshUser = async () => {
		const token = await getAuthToken();
		if (token) {
			queryClient.invalidateQueries({ queryKey: ["user"] });
		} else {
			setHasToken(false);
			clearUserData();
		}
	};

	return {
		user: data?.data ?? null,
		isLoading: hasToken === null || (hasToken && isLoading),
		error,
		isAuthenticated: hasToken === true && !!data?.success && !!data?.data,
		refreshUser,
	};
}
