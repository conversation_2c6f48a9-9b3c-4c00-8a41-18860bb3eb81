export class SecurityHeadersMiddleware {
	static getBasicHeaders(): Record<string, string> {
		const isDev = process.env.NODE_ENV === "development";
		return {
			"Strict-Transport-Security": "max-age=31536000; includeSubDomains",
			"X-Content-Type-Options": "nosniff",
			"X-Frame-Options": "DENY",
			// "Content-Security-Policy": isDev
			// 	? "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; object-src 'none'; connect-src 'self' ws:;"
			// 	: "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; object-src 'none';",
			"X-XSS-Protection": "1; mode=block",
			"Referrer-Policy": "strict-origin-when-cross-origin",
			"Permissions-Policy": "camera=(), microphone=(), geolocation=()",
		};
	}

	static addToResponse(response: Response): Response {
		const newHeaders = new Headers(response.headers);
		const securityHeaders = this.getBasicHeaders();
		for (const [key, value] of Object.entries(securityHeaders)) newHeaders.set(key, value);
		return new Response(response.body, {
			status: response.status,
			statusText: response.statusText,
			headers: newHeaders,
		});
	}
}
