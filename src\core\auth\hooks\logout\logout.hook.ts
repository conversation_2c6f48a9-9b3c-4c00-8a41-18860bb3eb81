import { useNavigatePaths } from "@/shared/hooks/utils";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { AuthCookieManager } from "../../services/auth-cookie.service";
import { toast } from "@/core/toast";
import { createDeleteRequest } from "@/shared/lib/requests";
import { AUTH_ENDPOINTS } from "../../api/endpoints";
import { useSetAtom } from "jotai";
import { userAtom } from "../../atoms/user.atom";
import { isAuthenticatedAtom } from "../../atoms/auth.atom";

interface ILogoutResponse {
	success: boolean;
}

interface IUseLogoutHook {
	logout: () => Promise<void>;
}

export const useLogout = (): IUseLogoutHook => {
	const { replaceToCurrent } = useNavigatePaths();
	const queryClient = useQueryClient();
	const setUser = useSetAtom(userAtom);
	const setIsAuthenticated = useSetAtom(isAuthenticatedAtom);

	const handleLogoutSuccess = async (): Promise<void> => {
		// Limpar tokens dos cookies
		await AuthCookieManager.clearTokens();

		// Limpar estado da aplicação imediatamente
		setUser(null);
		setIsAuthenticated(false);
		queryClient.clear();

		console.log("Tokens cleared and query cache cleared.");
		replaceToCurrent();
	};

	const logoutMutation = useMutation<ILogoutResponse, Error>({
		mutationFn: async (): Promise<ILogoutResponse> => {
			// Chama o backend externo para logout
			const response = await createDeleteRequest(AUTH_ENDPOINTS.LOGOUT);

			// Chama a rota local para remover cookies do servidor
			try {
				await fetch("/auth/logout", {
					method: "POST",
					headers: {
						"Content-Type": "application/json",
					},
				});
			} catch (error) {
				console.warn("Erro ao chamar rota local de logout:", error);
			}

			await handleLogoutSuccess();
			return response;
		},
	});

	return {
		logout: async (): Promise<void> => {
			await toast.promise(logoutMutation.mutateAsync(), {
				loading: "Saindo...",
				error: "Erro ao sair",
			});
		},
	};
};
