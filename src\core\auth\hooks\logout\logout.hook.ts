import { useNavigatePaths } from "@/shared/hooks/utils";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { AuthCookieManager } from "../../services/auth-cookie.service";
import { toast } from "@/core/toast";
import { createDeleteRequest } from "@/shared/lib/requests";
import { AUTH_ENDPOINTS } from "../../api/endpoints";

interface ILogoutResponse {
	success: boolean;
}

interface IUseLogoutHook {
	logout: () => Promise<void>;
}

export const useLogout = (): IUseLogoutHook => {
	const { replaceToCurrent } = useNavigatePaths();
	const queryClient = useQueryClient();

	const handleLogoutSuccess = async (): Promise<void> => {
		// await AuthCookieManager.clearTokens();
		queryClient.clear();
		console.log("Tokens cleared and query cache cleared.");
		replaceToCurrent();
	};

	const logoutMutation = useMutation<ILogoutResponse, Error>({
		mutationFn: async (): Promise<ILogoutResponse> => {
			const response = await createDeleteRequest(AUTH_ENDPOINTS.LOGOUT);

			await handleLogoutSuccess();
			return response;
		},
	});

	return {
		logout: async (): Promise<void> => {
			await toast.promise(logoutMutation.mutateAsync(), {
				loading: "Saindo...",
				error: "Erro ao sair",
			});
		},
	};
};
