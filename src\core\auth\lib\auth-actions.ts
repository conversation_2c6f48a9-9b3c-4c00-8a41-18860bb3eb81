"use server";

import { cookies } from "next/headers";

export const getAuthToken = async (): Promise<string | null> => {
	try {
		const cookieStore = await cookies();
		const cookieName = "access_token";
		const cookie = cookieStore.get(cookieName);
		return cookie?.value ?? null;
	} catch (error) {
		console.error("Erro ao obter token de acesso:", error);
		return null;
	}
};

export const getRefreshToken = async (): Promise<string | null> => {
	try {
		const cookieStore = await cookies();
		const cookieName = "refresh_token";
		const cookie = cookieStore.get(cookieName);
		return cookie?.value ?? null;
	} catch (error) {
		console.error("Erro ao obter token de atualização:", error);
		return null;
	}
}

export const isAuthenticated = async (): Promise<boolean> => {
	const token = await getAuthToken();
	return token !== null;
};
