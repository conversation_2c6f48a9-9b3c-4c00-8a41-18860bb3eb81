import * as AuthActions from "../lib/auth-actions";
import { removeCookie } from "@/shared/lib/cookies/crud/remove";

export class AuthCookieManager {
	public static readonly getAuthToken = AuthActions.getAuthToken;

	static async getAllTokens() {
		const accessToken = await this.getAuthToken();
		return { accessToken };
	}

	static async isAuthenticated() {
		return (await this.getAuthToken()) !== null;
	}

	static async clearTokens() {
		try {
			// Remove access token
			await removeCookie({ name: "access_token" });

			// Remove refresh token if it exists
			await removeCookie({ name: "refresh_token" });

			console.log("Tokens removidos com sucesso do navegador");
		} catch (error) {
			console.error("Erro ao remover tokens:", error);
		}
	}
}
