import { ICookie<PERSON><PERSON>erBuilder, ICookieData } from "@/shared/types/requests/cookie-header.type";

export class CookieHeaderService implements ICookieHeaderBuilder {
	private readonly excludedCookies = ["__next_hmr_refresh_hash__", "__next_hmr_refresh_hash", "__next_hmr_refresh_hash_"];

	public buildCookieHeader(cookies: Record<string, string>): string {
		const validCookies = Object.entries(cookies)
			.filter(([name, value]) => this.shouldIncludeCookie(name, value))
			.map(([name, value]) => `${name}=${value}`);

		return validCookies.join("; ");
	}

	public shouldIncludeCookie(cookieName: string, cookieValue: string): boolean {
		if (!cookieValue || this.excludedCookies.includes(cookieName)) {
			return false;
		}

		return cookieName.length > 0 && cookieValue.length > 0;
	}

	public processCookieData(cookieData: ICookieData): string | null {
		if (!cookieData.success || !cookieData.value) {
			return null;
		}

		const cookieHeader = this.buildCookieHeader(cookieData.value);
		return cookieHeader || null;
	}
}
