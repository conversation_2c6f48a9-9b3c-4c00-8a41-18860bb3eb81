import { AxiosInstance, InternalAxiosRequestConfig } from "axios";
import { getAllCookies } from "@/shared/lib/cookies/crud/get";
import { CookieHeaderService } from "@/shared/lib/cookies/services/cookie-header.service";

export interface ICookieHeadersInterceptorConfig {
	requiredCookies?: string[];
	excludedCookies?: string[];
	includeAllCookies?: boolean;
}

export const cookieHeadersInterceptor = (instance: AxiosInstance): void => {
	const cookieService = new CookieHeaderService();

	instance.interceptors.request.use(async (requestConfig: InternalAxiosRequestConfig) => {
		try {
			const cookiesData = await getAllCookies();
			const cookieHeader = cookieService.processCookieData(cookiesData);

			if (cookieHeader) {
				requestConfig.headers = requestConfig.headers || {};
				requestConfig.headers.Cookie = cookieHeader;
			}
		} catch (error) {
			console.error("Erro ao processar cookies para requisição:", error);
		}


		return requestConfig;
	});
};
