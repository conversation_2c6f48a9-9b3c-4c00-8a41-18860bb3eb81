"use server";

import { ApiResponse } from "@/shared/types/requests/request.type";
import { IUser } from "../types/user.types";
import { createGetRequest } from "@/shared/lib/requests";
import { IRole } from "@/config/enums/role.enum";
import { AUTH_ENDPOINTS } from "../api/endpoints";
import { AUTH_TIMEOUTS, AUTH_RETRY_CONFIG } from "../constants/auth-timeouts";

type IGetCurrentUserResponse = {
	id: number;
	name: string;
	document: string;
	roles: IRole[];
};

export const getCurrentUser = async (): Promise<ApiResponse<IUser>> => {
	const res = await createGetRequest<IGetCurrentUserResponse>(AUTH_ENDPOINTS.PROFILE, {
		timeout: AUTH_TIMEOUTS.USER_PROFILE,
		retry: AUTH_RETRY_CONFIG.DEFAULT_RETRY,
		retryAttempts: AUTH_RETRY_CONFIG.USER_PROFILE_MAX_RETRIES,
	});
	// console.log("res", res);
	if (!res.success) return res;
	const { id, name, roles } = res.data;
	return {
		success: true,
		status: 200,
		data: {
			id: id?.toString() || "",
			name: name || "",
			permissions: roles || [],
		},
	};
};
