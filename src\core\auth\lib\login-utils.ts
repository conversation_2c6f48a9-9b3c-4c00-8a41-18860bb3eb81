import { axiosInstance } from "@/config/api/instance";

interface ILoginRequestParams {
	redirectPath: string;
}

interface IBackendResponse {
	status: number;
	location?: string;
	errorText?: string;
}

interface ILoginErrorResponse {
	error: string;
	message: string;
}

export function extractLoginParams(request: URL): ILoginRequestParams {
	const { searchParams } = request;
	return {
		redirectPath: searchParams.get("redirect") || "/",
	};
}

export function createErrorResponse(error: string, message: string, status: number): Response {
	return Response.json({ error, message } as ILoginErrorResponse, { status });
}

export async function fetchBackendLoginUrl(redirectPath: string): Promise<IBackendResponse> {
	const params = new URLSearchParams();
	if (redirectPath !== "/") params.set("redirect", redirectPath);
	
	try {
		const response = await axiosInstance.get("/auth/login", {
			params: redirectPath !== "/" ? { redirect: redirectPath } : undefined,
			maxRedirects: 0,
			validateStatus: (status) => status < 400,
			withCredentials: true,
		});
		return {
			status: response.status,
			location: response.headers.location || undefined,
			errorText: response.status !== 302 ? response.data : undefined,
		};
	} catch (error: any) {
		if (error.response && error.response.status === 302) {
	
			return {
				status: error.response.status,
				location: error.response.headers.location || undefined,
				errorText: undefined,
			};
		}
		return {
			status: error.response?.status || 500,
			location: undefined,
			errorText: error.response?.data || error.message,
		};
	}
}

export function isRedirectResponse(response: IBackendResponse): boolean {
	return response.status === 302;
}

export function handleRedirectResponse(response: IBackendResponse): Response {
	const { location } = response;
	if (!location) throw new Error("URL de redirecionamento não encontrada na resposta do backend");
	return Response.redirect(location, 302);
}

export function handleBackendError(response: IBackendResponse): Response {
	return createErrorResponse("backend_error", "Erro na comunicação com o servidor de autenticação", 500);
}

export function isValidRedirectPath(path: string): boolean {
	const hasProtocol = path.includes("://") || path.startsWith("//");
	const hasValidStart = path.startsWith("/");
	const hasDangerousChars = ["<", ">", '"', "'", "&"].some(char => path.includes(char));
	if (hasProtocol || !hasValidStart || hasDangerousChars) return false;
	const allowedPaths = ["/", "/variaveis", "/forbidden"];
	return allowedPaths.some(allowedPath => path === allowedPath || path.startsWith(allowedPath + "/"));
}
